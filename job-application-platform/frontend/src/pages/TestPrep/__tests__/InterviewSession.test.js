import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import InterviewSession from '../InterviewSession';
import testPrepReducer from '../../../redux/slices/testPrepSlice';
import uiReducer from '../../../redux/slices/uiSlice';

// Mock the PageHeader component
jest.mock('../../../components/common/PageHeader', () => {
  return function MockPageHeader({ title, subtitle }) {
    return (
      <div data-testid="page-header">
        <h1>{title}</h1>
        <p>{subtitle}</p>
      </div>
    );
  };
});

// Mock react-router-dom hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ id: 'interview-session-123' }),
  useNavigate: () => jest.fn(),
}));

const theme = createTheme();

const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      testPrep: testPrepReducer,
      ui: uiReducer,
    },
    preloadedState: {
      testPrep: {
        currentInterviewSession: null,
        loading: false,
        error: null,
        ...initialState.testPrep,
      },
      ui: {
        alerts: [],
        ...initialState.ui,
      },
    },
  });
};

const TestWrapper = ({ children, initialState = {} }) => {
  const store = createTestStore(initialState);
  
  return (
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {children}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('InterviewSession Component - charAt() Error Fix', () => {
  
  test('renders without crashing when currentInterviewSession is null', () => {
    render(
      <TestWrapper initialState={{ testPrep: { currentInterviewSession: null } }}>
        <InterviewSession />
      </TestWrapper>
    );
    
    // Should show "Interview session not found" message instead of crashing
    expect(screen.getByText(/interview session not found/i)).toBeInTheDocument();
  });

  test('renders without crashing when currentInterviewSession has undefined mode and jobRole', () => {
    const interviewSessionWithUndefinedFields = {
      _id: 'interview-123',
      title: 'Interview Session',
      mode: undefined, // This would cause charAt() error
      jobRole: undefined, // This could also cause issues
      turns: [],
      status: 'active',
    };

    render(
      <TestWrapper initialState={{ testPrep: { currentInterviewSession: interviewSessionWithUndefinedFields } }}>
        <InterviewSession />
      </TestWrapper>
    );
    
    // Should render with fallback values instead of crashing
    expect(screen.getByText('Practice')).toBeInTheDocument(); // Default mode fallback (capitalized)
    expect(screen.getByText('General Interview')).toBeInTheDocument(); // Default jobRole fallback
    expect(screen.getByText('Interview Session')).toBeInTheDocument(); // Title should render
  });

  test('renders correctly when currentInterviewSession has valid mode and jobRole', () => {
    const validInterviewSession = {
      _id: 'interview-123',
      title: 'Software Engineer Interview',
      mode: 'voice',
      jobRole: 'Software Engineer',
      turns: [
        {
          speaker: 'ai',
          text: 'Hello! Welcome to your interview.',
        },
      ],
      status: 'active',
    };

    render(
      <TestWrapper initialState={{ testPrep: { currentInterviewSession: validInterviewSession } }}>
        <InterviewSession />
      </TestWrapper>
    );
    
    // Should render with proper capitalization
    expect(screen.getByText('Voice')).toBeInTheDocument(); // Mode should be capitalized
    expect(screen.getByText('Software Engineer Interview')).toBeInTheDocument();
    expect(screen.getByText('Hello! Welcome to your interview.')).toBeInTheDocument();
  });

  test('handles completed interview session without crashing', () => {
    const completedInterviewSession = {
      _id: 'interview-123',
      title: 'Completed Interview',
      mode: undefined, // Test undefined mode in completed state
      jobRole: undefined, // Test undefined jobRole in completed state
      status: 'completed',
      overallRating: 4.5,
      feedback: [
        {
          text: 'Great communication skills demonstrated throughout the interview.',
          rating: 5,
        },
      ],
    };

    render(
      <TestWrapper initialState={{ testPrep: { currentInterviewSession: completedInterviewSession } }}>
        <InterviewSession />
      </TestWrapper>
    );
    
    // Should show completion message without crashing
    expect(screen.getByText(/interview complete/i)).toBeInTheDocument();
    expect(screen.getByText('4.5')).toBeInTheDocument(); // Overall rating
  });

  test('handles empty turns array without crashing', () => {
    const interviewSessionWithEmptyTurns = {
      _id: 'interview-123',
      title: 'New Interview',
      mode: 'text',
      jobRole: 'Product Manager',
      turns: [], // Empty turns array
      status: 'active',
    };

    render(
      <TestWrapper initialState={{ testPrep: { currentInterviewSession: interviewSessionWithEmptyTurns } }}>
        <InterviewSession />
      </TestWrapper>
    );
    
    // Should render the interface without any conversation turns
    expect(screen.getByText('Text')).toBeInTheDocument();
    expect(screen.getByText('Product Manager Interview')).toBeInTheDocument();
    expect(screen.getByPlaceholderText(/type your response/i)).toBeInTheDocument();
  });
});
