import React from 'react';
import { render, screen } from '@testing-library/react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import TestSession from '../TestSession';
import testPrepReducer from '../../../redux/slices/testPrepSlice';
import uiReducer from '../../../redux/slices/uiSlice';

// Mock the PageHeader component
jest.mock('../../../components/common/PageHeader', () => {
  return function MockPageHeader({ title, subtitle }) {
    return (
      <div data-testid="page-header">
        <h1>{title}</h1>
        <p>{subtitle}</p>
      </div>
    );
  };
});

// Mock the Timer component
jest.mock('../../../components/common/Timer', () => {
  return function MockTimer() {
    return <div data-testid="timer">Timer</div>;
  };
});

// Mock the CircularProgressWithLabel component
jest.mock('../../../components/common/CircularProgressWithLabel', () => {
  return function MockCircularProgressWithLabel({ value }) {
    return <div data-testid="progress-circle">{value}%</div>;
  };
});

// Mock react-router-dom hooks
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ id: 'test-session-123' }),
  useNavigate: () => jest.fn(),
}));

const theme = createTheme();

const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      testPrep: testPrepReducer,
      ui: uiReducer,
    },
    preloadedState: {
      testPrep: {
        currentTestSession: null,
        loading: false,
        error: null,
        ...initialState.testPrep,
      },
      ui: {
        alerts: [],
        ...initialState.ui,
      },
    },
  });
};

const TestWrapper = ({ children, initialState = {} }) => {
  const store = createTestStore(initialState);
  
  return (
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          {children}
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

describe('TestSession Component - charAt() Error Fix', () => {
  test('renders without crashing when currentTestSession is null', () => {
    render(
      <TestWrapper initialState={{ testPrep: { currentTestSession: null } }}>
        <TestSession />
      </TestWrapper>
    );
    
    // Should show "Test session not found" message instead of crashing
    expect(screen.getByText(/test session not found/i)).toBeInTheDocument();
  });

  test('renders without crashing when currentTestSession has undefined mode', () => {
    const testSessionWithUndefinedMode = {
      _id: 'test-123',
      title: 'Test Session',
      mode: undefined, // This would cause charAt() error
      difficulty: undefined, // This would also cause charAt() error
      questions: [
        {
          _id: 'q1',
          text: 'What is React?',
          options: [
            { label: 'A', text: 'A library' },
            { label: 'B', text: 'A framework' },
          ],
        },
      ],
      completedQuestions: 0,
      totalQuestions: 1,
      status: 'active',
    };

    render(
      <TestWrapper initialState={{ testPrep: { currentTestSession: testSessionWithUndefinedMode } }}>
        <TestSession />
      </TestWrapper>
    );
    
    // Should render with fallback values instead of crashing
    expect(screen.getByText('Practice Mode')).toBeInTheDocument(); // Default mode fallback
    expect(screen.getByText('Test Session')).toBeInTheDocument(); // Title should render
    expect(screen.getByText('Question 1:')).toBeInTheDocument(); // Question should render
  });

  test('renders correctly when currentTestSession has valid mode and difficulty', () => {
    const validTestSession = {
      _id: 'test-123',
      title: 'JavaScript Test',
      mode: 'simulation',
      difficulty: 'hard',
      questions: [
        {
          _id: 'q1',
          text: 'What is React?',
          options: [
            { label: 'A', text: 'A library' },
            { label: 'B', text: 'A framework' },
          ],
        },
      ],
      completedQuestions: 0,
      totalQuestions: 1,
      status: 'active',
    };

    render(
      <TestWrapper initialState={{ testPrep: { currentTestSession: validTestSession } }}>
        <TestSession />
      </TestWrapper>
    );
    
    // Should render with proper capitalization
    expect(screen.getByText('Simulation Mode')).toBeInTheDocument();
    expect(screen.getByText('Hard')).toBeInTheDocument();
    expect(screen.getByText('JavaScript Test')).toBeInTheDocument();
  });

  test('handles completed test session without crashing', () => {
    const completedTestSession = {
      _id: 'test-123',
      title: 'Completed Test',
      mode: undefined, // Test undefined mode in completed state
      status: 'completed',
      isComplete: true,
      report: {
        score: 85,
      },
    };

    render(
      <TestWrapper initialState={{ testPrep: { currentTestSession: completedTestSession } }}>
        <TestSession />
      </TestWrapper>
    );
    
    // Should show completion message without crashing
    expect(screen.getByText(/congratulations/i)).toBeInTheDocument();
    expect(screen.getByText('85%')).toBeInTheDocument();
  });
});
