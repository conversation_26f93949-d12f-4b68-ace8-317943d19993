import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Divider,
  CircularProgress,
  Avatar,
  IconButton,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Chip,
  Tooltip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Zoom,
  useTheme,
} from '@mui/material';
import {
  Send,
  Mic,
  MicOff,
  Stop,
  CheckCircle,
  Close,
  ArrowBack,
  PersonOutline,
  SmartToy,
  Warning,
  RecordVoiceOver,
  QuestionAnswer,
} from '@mui/icons-material';
import { 
  fetchInterviewSession, 
  submitInterviewTurn,
  clearCurrentInterviewSession 
} from '../../redux/slices/testPrepSlice';
import { setAlert } from '../../redux/slices/uiSlice';
import PageHeader from '../../components/common/PageHeader';
import LoadingSpinner from '../../components/common/LoadingSpinner';

// Rating component
const Rating = ({ value }) => {
  const theme = useTheme();
  const stars = [];
  
  for (let i = 1; i <= 5; i++) {
    const color = i <= value ? theme.palette.warning.main : theme.palette.grey[300];
    stars.push(
      <Box
        key={i}
        sx={{
          width: 20,
          height: 20,
          borderRadius: '50%',
          backgroundColor: color,
          mr: 0.5,
        }}
      />
    );
  }
  
  return <Box sx={{ display: 'flex' }}>{stars}</Box>;
};

const InterviewSession = () => {
  const theme = useTheme();
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const messagesEndRef = useRef(null);

  const { currentInterviewSession, loading, error } = useSelector(
    (state) => state.testPrep
  );

  // Local state
  const [response, setResponse] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState('');
  const [confirmExitDialog, setConfirmExitDialog] = useState(false);
  const [isThinking, setIsThinking] = useState(false);
  const [displayingFeedback, setDisplayingFeedback] = useState(false);
  
  // Fetch interview session on component mount
  useEffect(() => {
    dispatch(fetchInterviewSession(id));

    return () => {
      // Cleanup on component unmount
      dispatch(clearCurrentInterviewSession());
    };
  }, [dispatch, id]);
  
  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [currentInterviewSession?.turns]);

  // Handle response change
  const handleResponseChange = (e) => {
    setResponse(e.target.value);
  };

  // Submit response
  const handleSubmit = () => {
    if (!response.trim() && !audioUrl) {
      dispatch(
        setAlert({
          message: 'Please enter a response',
          type: 'warning',
        })
      );
      return;
    }

    setIsThinking(true);
    
    dispatch(submitInterviewTurn({ id, text: response, audioUrl }))
      .unwrap()
      .then((result) => {
        setResponse('');
        setAudioUrl('');
        setIsThinking(false);
        
        if (result.isComplete) {
          setDisplayingFeedback(true);
        }
      })
      .catch((error) => {
        setIsThinking(false);
        console.error('Interview response error:', error);
        
        // Show user-friendly error message
        dispatch(
          setAlert({
            message: 'Connection issue detected. Please check your internet connection and try again.',
            type: 'warning',
          })
        );
      });
  };

  // Handle voice recording (stub - would need actual implementation)
  const toggleRecording = () => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false);
      // In a real implementation, would process audio and set audioUrl
      setAudioUrl('mock-audio-url');
    } else {
      // Start recording
      setIsRecording(true);
      setAudioUrl('');
    }
  };

  // Handle exit interview
  const handleExitInterview = () => {
    navigate('/app/test-prep');
  };

  // Handle view feedback
  const handleViewFeedback = () => {
    navigate(`/app/test-prep/interview-feedback/${id}`);
  };

  // Loading state
  if (loading && !currentInterviewSession) {
    return <LoadingSpinner message="Loading interview session..." />;
  }

  // Error state
  if (error && !currentInterviewSession) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {error || 'Failed to load interview session.'}
        </Alert>
        <Button
          variant="contained"
          color="primary"
          sx={{ mt: 2 }}
          onClick={() => navigate('/app/test-prep')}
        >
          Return to Dashboard
        </Button>
      </Box>
    );
  }

  // No interview session found
  if (!currentInterviewSession) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Interview session not found or has expired.
        </Alert>
        <Button
          variant="contained"
          color="primary"
          sx={{ mt: 2 }}
          onClick={() => navigate('/app/test-prep')}
        >
          Return to Dashboard
        </Button>
      </Box>
    );
  }

  // Interview completed state (if feedback is being shown)
  if (displayingFeedback || currentInterviewSession?.status === 'completed') {
    return (
      <Box>
        <PageHeader
          title="Interview Complete"
          subtitle={currentInterviewSession.title}
          breadcrumbs={[
            { text: 'Test Prep', link: '/app/test-prep' },
            { text: 'Interview Results' },
          ]}
        />
        
        <Paper elevation={0} sx={{ p: 3, borderRadius: 2, mb: 3, textAlign: 'center' }}>
          <Box sx={{ mb: 3 }}>
            <CheckCircle sx={{ fontSize: 80, color: theme.palette.success.main, mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              Your mock interview is complete!
            </Typography>
            <Typography variant="body1" color="text.secondary">
              You can now view your feedback and performance analysis
            </Typography>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
            <Card elevation={0} sx={{ maxWidth: 500, width: '100%', borderRadius: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Overall Performance
                </Typography>
                
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h3" color="primary" fontWeight="bold" sx={{ mr: 2 }}>
                    {currentInterviewSession.overallRating || 4.0}
                  </Typography>
                  <Rating value={currentInterviewSession.overallRating || 4} />
                </Box>
                
                <Divider sx={{ mb: 2 }} />
                
                <Typography variant="subtitle1" gutterBottom>
                  Key Strengths:
                </Typography>
                <List dense>
                  {currentInterviewSession.feedback?.filter(f => f.rating >= 4)
                    .slice(0, 2)
                    .map((feedback, index) => (
                      <ListItem key={index}>
                        <ListItemAvatar>
                          <Avatar sx={{ backgroundColor: theme.palette.success.light }}>
                            <CheckCircle />
                          </Avatar>
                        </ListItemAvatar>
                        <ListItemText 
                          primary={feedback.text.substring(0, 80) + '...'} 
                        />
                      </ListItem>
                    ))
                  }
                  {(!currentInterviewSession.feedback || currentInterviewSession.feedback.length === 0) && (
                    <ListItem>
                      <ListItemText primary="Clearly articulated responses" />
                    </ListItem>
                  )}
                </List>
              </CardContent>
            </Card>
          </Box>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleViewFeedback}
              startIcon={<QuestionAnswer />}
            >
              View Detailed Feedback
            </Button>
            <Button
              variant="outlined"
              color="primary"
              onClick={() => navigate('/app/test-prep')}
            >
              Return to Dashboard
            </Button>
          </Box>
        </Paper>
      </Box>
    );
  }

  // Interview in progress
  const turns = currentInterviewSession.turns || [];

  return (
    <Box>
      <PageHeader
        title={currentInterviewSession?.title || 'Interview Session'}
        subtitle={`Mock Interview: ${currentInterviewSession?.jobRole || 'General'}`}
        breadcrumbs={[
          { text: 'Test Prep', link: '/app/test-prep' },
          { text: 'Interview Session' },
        ]}
      />
      
      {/* Interview Session UI */}
      <Paper elevation={0} sx={{ borderRadius: 2, mb: 3, overflow: 'hidden' }}>
        {/* Header */}
        <Box sx={{ 
          p: 2, 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          bgcolor: theme.palette.grey[50],
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <RecordVoiceOver sx={{ mr: 1 }} />
            <Typography variant="subtitle1" fontWeight={500}>
              {currentInterviewSession?.jobRole || 'General'} Interview
            </Typography>
            <Chip
              label={(currentInterviewSession?.mode || 'practice').charAt(0).toUpperCase() + (currentInterviewSession?.mode || 'practice').slice(1)}
              color="primary"
              size="small"
              sx={{ ml: 2 }}
            />
          </Box>
          
          <IconButton 
            color="inherit" 
            onClick={() => setConfirmExitDialog(true)}
          >
            <Close />
          </IconButton>
        </Box>
        
        {/* Chat area */}
        <Box 
          sx={{ 
            p: 3, 
            maxHeight: '60vh', 
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {turns.map((turn, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                flexDirection: turn.speaker === 'ai' ? 'row' : 'row-reverse',
                mb: 2,
              }}
            >
              <Avatar
                sx={{
                  bgcolor: turn.speaker === 'ai' ? 'primary.main' : 'secondary.main',
                  mr: turn.speaker === 'ai' ? 2 : 0,
                  ml: turn.speaker === 'user' ? 2 : 0,
                }}
              >
                {turn.speaker === 'ai' ? <SmartToy /> : <PersonOutline />}
              </Avatar>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  borderRadius: 2,
                  maxWidth: '70%',
                  bgcolor: turn.speaker === 'ai' ? theme.palette.grey[50] : theme.palette.primary.light,
                  color: turn.speaker === 'ai' ? 'text.primary' : theme.palette.primary.contrastText,
                }}
              >
                <Typography variant="body1">{turn.text}</Typography>
              </Paper>
            </Box>
          ))}
          
          {isThinking && (
            <Box
              sx={{
                display: 'flex',
                mb: 2,
              }}
            >
              <Avatar
                sx={{
                  bgcolor: 'primary.main',
                  mr: 2,
                }}
              >
                <SmartToy />
              </Avatar>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  borderRadius: 2,
                  bgcolor: theme.palette.grey[50],
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <CircularProgress size={20} sx={{ mr: 2 }} />
                <Typography variant="body2" color="text.secondary">
                  Thinking...
                </Typography>
              </Paper>
            </Box>
          )}
          
          <div ref={messagesEndRef} />
        </Box>
        
        {/* Input area */}
        <Divider />
        <Box sx={{ p: 2 }}>
          <Box
            component="form"
            onSubmit={(e) => {
              e.preventDefault();
              handleSubmit();
            }}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
            }}
          >
            <TextField
              fullWidth
              multiline
              rows={3}
              placeholder="Type your response here..."
              value={response}
              onChange={handleResponseChange}
              disabled={isThinking}
              InputProps={{
                sx: { borderRadius: 2 },
              }}
            />
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {currentInterviewSession?.mode === 'voice' && (
                <Tooltip title={isRecording ? 'Stop recording' : 'Start recording'}>
                  <span>
                    <IconButton
                      color={isRecording ? 'error' : 'primary'}
                      onClick={toggleRecording}
                      disabled={isThinking}
                    >
                      {isRecording ? <Stop /> : <Mic />}
                    </IconButton>
                  </span>
                </Tooltip>
              )}
              
              <Button
                variant="contained"
                color="primary"
                disabled={isThinking || (!response.trim() && !audioUrl)}
                onClick={handleSubmit}
                sx={{ borderRadius: 2, px: 3 }}
              >
                {isThinking ? (
                  <CircularProgress size={24} />
                ) : (
                  <Send />
                )}
              </Button>
            </Box>
          </Box>
          
          {audioUrl && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <Chip
                label="Audio response recorded"
                color="success"
                onDelete={() => setAudioUrl('')}
                sx={{ mr: 1 }}
              />
            </Box>
          )}
        </Box>
      </Paper>

      {/* Tips and advice */}
      <Paper elevation={0} sx={{ p: 3, borderRadius: 2, bgcolor: theme.palette.background.default }}>
        <Typography variant="subtitle1" fontWeight={500} gutterBottom>
          Interview Tips
        </Typography>
        <List dense disablePadding>
          <ListItem>
            <ListItemText 
              primary="Use the STAR method (Situation, Task, Action, Result) when answering behavioral questions."
            />
          </ListItem>
          <ListItem>
            <ListItemText 
              primary="Be specific and use real examples from your past experiences."
            />
          </ListItem>
          <ListItem>
            <ListItemText 
              primary="Keep your answers concise but complete, typically 1-2 minutes per question."
            />
          </ListItem>
        </List>
      </Paper>

      {/* Confirm Exit Dialog */}
      <Dialog
        open={confirmExitDialog}
        onClose={() => setConfirmExitDialog(false)}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Warning color="warning" sx={{ mr: 1 }} />
            Exit Interview Session?
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to exit this interview? Your progress will be saved, but the interview will be marked as abandoned.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmExitDialog(false)}>
            Continue Interview
          </Button>
          <Button onClick={handleExitInterview} color="error">
            Exit Interview
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default InterviewSession;