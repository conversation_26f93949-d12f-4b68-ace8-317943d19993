import React, { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  CircularProgress,
  LinearProgress,
  RadioGroup,
  FormControlLabel,
  Radio,
  Card,
  CardContent,
  Divider,
  Chip,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  IconButton,
  useTheme,
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  Flag,
  AccessTime,
  CheckCircle,
  Cancel,
  Help,
  Close,
  WatchLater,
  Warning,
  EmojiEvents,
} from '@mui/icons-material';
import {
  fetchTestSession,
  submitTestAnswer,
  clearCurrentTestSession
} from '../../redux/slices/testPrepSlice';
import { setAlert } from '../../redux/slices/uiSlice';
import PageHeader from '../../components/common/PageHeader';
import LoadingSpinner from '../../components/common/LoadingSpinner';

// Helper function to safely capitalize strings
const safeCapitalize = (str) => {
  if (!str || typeof str !== 'string' || str.length === 0) {
    return '';
  }
  return str.charAt(0).toUpperCase() + str.slice(1);
};

// Circular progress with label component
const CircularProgressWithLabel = ({ value, size = 70 }) => {
  const theme = useTheme();

  const getColor = () => {
    if (value >= 70) return theme.palette.success.main;
    if (value >= 40) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  return (
    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
      <CircularProgress
        variant="determinate"
        value={value}
        size={size}
        thickness={4}
        sx={{ color: getColor() }}
      />
      <Box
        sx={{
          top: 0,
          left: 0,
          bottom: 0,
          right: 0,
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography variant="caption" component="div" fontWeight="bold">
          {`${Math.round(value)}%`}
        </Typography>
      </Box>
    </Box>
  );
};

// Timer component
const Timer = ({ seconds, isRunning, onTimeUp }) => {
  const [timeLeft, setTimeLeft] = useState(seconds);

  useEffect(() => {
    setTimeLeft(seconds);
  }, [seconds]);

  useEffect(() => {
    let timer;

    if (isRunning && timeLeft > 0) {
      timer = setInterval(() => {
        setTimeLeft((prev) => {
          const newTime = prev - 1;
          if (newTime <= 0 && onTimeUp) {
            onTimeUp();
            clearInterval(timer);
          }
          return newTime;
        });
      }, 1000);
    }

    return () => clearInterval(timer);
  }, [isRunning, timeLeft, onTimeUp]);

  // Format time as mm:ss
  const formatTime = () => {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Get color based on time left
  const getColor = () => {
    if (timeLeft > 60) return 'primary.main';
    if (timeLeft > 30) return 'warning.main';
    return 'error.main';
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <WatchLater sx={{ color: getColor(), mr: 1 }} />
      <Typography variant="h6" sx={{ color: getColor() }}>
        {formatTime()}
      </Typography>
    </Box>
  );
};

const TestSession = () => {
  const theme = useTheme();
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // Debug logging
  console.log('[TestSession] Route params:', { id });
  console.log('[TestSession] ID type:', typeof id);
  console.log('[TestSession] ID length:', id?.length);
  console.log('[TestSession] ID truthy check:', !!id);
  console.log('[TestSession] Current URL:', window.location.href);

  const { currentTestSession, loading, error } = useSelector(
    (state) => state.testPrep
  );

  // Local state
  const [currentAnswer, setCurrentAnswer] = useState('');
  const [timeSpent, setTimeSpent] = useState(0);
  const [timer, setTimer] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [isTimerRunning, setIsTimerRunning] = useState(true);
  const [confirmAbortDialog, setConfirmAbortDialog] = useState(false);
  const startTimeRef = useRef(null);

  // Handle option change
  const handleOptionChange = (e) => {
    setCurrentAnswer(e.target.value);
  };

  // Submit answer
  const handleSubmit = () => {
    if (!currentAnswer) {
      dispatch(
        setAlert({
          message: 'Please select an answer',
          type: 'warning',
        })
      );
      return;
    }

    const endTime = Date.now();
    const questionTimeSpent = Math.round((endTime - startTimeRef.current) / 1000);

    dispatch(submitTestAnswer({ id, questionId: currentQuestion._id, answer: currentAnswer }))
      .unwrap()
      .then(() => {
        // Reset for next question
        setCurrentAnswer('');
        startTimeRef.current = Date.now();
      })
      .catch((error) => {
        dispatch(
          setAlert({
            message: error || 'Failed to submit answer',
            type: 'error',
          })
        );
      });
  };

  // Fetch test session on component mount
  useEffect(() => {
    console.log('[TestSession] useEffect triggered with id:', id, 'type:', typeof id, 'length:', id?.length);
    
    // More robust check for valid ID
    if (id && id !== 'undefined' && id.trim().length > 0 && id.length === 24) {
      console.log('[TestSession] Valid ID detected, fetching test session:', id);
      dispatch(fetchTestSession(id));
    } else {
      console.error('[TestSession] Invalid or missing test session ID:', { 
        id, 
        type: typeof id, 
        length: id?.length,
        trimmed: id?.trim(),
        urlParams: window.location.pathname,
        urlSearch: window.location.search
      });
      dispatch(
        setAlert({
          message: 'Invalid test session ID provided. Please navigate from the test prep dashboard.',
          type: 'error',
        })
      );
      navigate('/app/test-prep');
    }

    return () => {
      // Cleanup on component unmount
      dispatch(clearCurrentTestSession());
    };
  }, [dispatch, id, navigate]);

  // Initialize timer when test session loads
  useEffect(() => {
    if (currentTestSession && !startTimeRef.current) {
      startTimeRef.current = Date.now();

      // Set timer if specified in test settings
      if (currentTestSession.timeLimit) {
        // Convert minutes to seconds
        setTimer(currentTestSession.timeLimit * 60);
      }
    }
  }, [currentTestSession]);

  // Get current question
  const getCurrentQuestion = () => {
    if (!currentTestSession || !currentTestSession.questions) return null;

    const questionIndex = currentTestSession.completedQuestions || 0;
    if (questionIndex >= currentTestSession.questions.length) return null;

    return currentTestSession.questions[questionIndex];
  };

  // Get last answer details (for feedback in practice mode)
  const getLastAnswerDetails = () => {
    return currentTestSession?.lastAnswer || null;
  };

  // Calculate progress percentage
  const getProgressPercentage = () => {
    if (!currentTestSession) return 0;

    const { completedQuestions, totalQuestions } = currentTestSession;
    return (completedQuestions / totalQuestions) * 100;
  };

  // Handle time up
  const handleTimeUp = () => {
    dispatch(
      setAlert({
        message: 'Time is up! Submitting your test.',
        type: 'warning',
      })
    );

    // Auto-submit with empty answer or current answer
    const currentQuestion = getCurrentQuestion();
    dispatch(submitTestAnswer({
      id,
      questionId: currentQuestion?._id || 'unknown',
      answer: currentAnswer || 'X' // X for no answer
    }))
      .unwrap()
      .then(() => {
        setDialogOpen(true);
      })
      .catch((error) => {
        dispatch(
          setAlert({
            message: error || 'Failed to submit test',
            type: 'error',
          })
        );
      });
  };

  // Handle abort test
  const handleAbortTest = () => {
    navigate('/app/test-prep');
  };

  // Handle view results
  const handleViewResults = () => {
    navigate(`/app/test-prep/reports/${id}`);
  };

  // Loading state
  if (loading && !currentTestSession) {
    return <LoadingSpinner message="Loading test session..." />;
  }

  // Error state
  if (error && !currentTestSession) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          {error || 'Failed to load test session.'}
        </Alert>
        <Button
          variant="contained"
          color="primary"
          sx={{ mt: 2 }}
          onClick={() => navigate('/app/test-prep')}
        >
          Return to Dashboard
        </Button>
      </Box>
    );
  }

  // No test session found
  if (!currentTestSession) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">
          Test session not found or has expired.
        </Alert>
        <Button
          variant="contained"
          color="primary"
          sx={{ mt: 2 }}
          onClick={() => navigate('/app/test-prep')}
        >
          Return to Dashboard
        </Button>
      </Box>
    );
  }

  // Test completed state
  if (currentTestSession?.status === 'completed' || currentTestSession?.isComplete) {
    return (
      <Box>
        <PageHeader
          title="Test Complete"
          subtitle={currentTestSession.title}
          breadcrumbs={[
            { text: 'Test Prep', link: '/app/test-prep' },
            { text: 'Test Results' },
          ]}
        />

        <Paper elevation={0} sx={{ p: 3, borderRadius: 2, mb: 3, textAlign: 'center' }}>
          <Box sx={{ mb: 3 }}>
            <EmojiEvents sx={{ fontSize: 80, color: theme.palette.primary.main, mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              Congratulations! You've completed the test!
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Your results are ready to view
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 4 }}>
            <CircularProgressWithLabel
              value={currentTestSession.report?.score || 0}
              size={120}
            />
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleViewResults}
              startIcon={<CheckCircle />}
            >
              View Detailed Results
            </Button>
            <Button
              variant="outlined"
              color="primary"
              onClick={() => navigate('/app/test-prep')}
            >
              Return to Dashboard
            </Button>
          </Box>
        </Paper>
      </Box>
    );
  }

  // Get current question
  const currentQuestion = getCurrentQuestion();
  const lastAnswer = getLastAnswerDetails();
  const progressPercentage = getProgressPercentage();

  return (
    <Box>
      <PageHeader
        title={currentTestSession?.title || 'Test Session'}
        subtitle={`Question ${(currentTestSession?.completedQuestions || 0) + 1} of ${currentTestSession?.totalQuestions || 0}`}
        breadcrumbs={[
          { text: 'Test Prep', link: '/app/test-prep' },
          { text: 'Test Session' },
        ]}
      />

      {/* Test Session UI */}
      <Card elevation={0} sx={{ borderRadius: 2, mb: 3, overflow: 'hidden' }}>
        {/* Progress bar */}
        <LinearProgress
          variant="determinate"
          value={progressPercentage}
          sx={{ height: 8 }}
        />

        {/* Header */}
        <Box sx={{
          p: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          bgcolor: theme.palette.grey[50],
          borderBottom: `1px solid ${theme.palette.divider}`,
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography variant="subtitle1" fontWeight={500} sx={{ mr: 2 }}>
              {currentTestSession?.title || 'Test Session'}
            </Typography>
            <Chip
              label={`${safeCapitalize(currentTestSession?.mode || 'practice')} Mode`}
              color="primary"
              size="small"
            />
            {currentTestSession?.difficulty && (
              <Chip
                label={safeCapitalize(currentTestSession?.difficulty || '')}
                color="secondary"
                size="small"
                sx={{ ml: 1 }}
              />
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {/* Timer */}
            {timer && (
              <Timer
                seconds={timer}
                isRunning={isTimerRunning}
                onTimeUp={handleTimeUp}
              />
            )}

            {/* Abort button */}
            <IconButton
              color="error"
              sx={{ ml: 2 }}
              onClick={() => setConfirmAbortDialog(true)}
            >
              <Close />
            </IconButton>
          </Box>
        </Box>

        <CardContent sx={{ p: 3 }}>
          {/* Question */}
          {currentQuestion && (
            <Box>
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  Question {(currentTestSession?.completedQuestions || 0) + 1}:
                </Typography>
                <Typography variant="body1" paragraph>
                  {currentQuestion.text}
                </Typography>
              </Box>

              {/* Answer options */}
              <RadioGroup
                name="answer"
                value={currentAnswer}
                onChange={handleOptionChange}
              >
                {currentQuestion.options.map((option, index) => (
                  <FormControlLabel
                    key={index}
                    value={option.label}
                    control={<Radio />}
                    label={
                      <Typography>
                        <strong>{option.label}:</strong> {option.text}
                      </Typography>
                    }
                    sx={{
                      mb: 1.5,
                      p: 1.5,
                      borderRadius: 1,
                      border: 1,
                      borderColor: 'divider',
                      '&:hover': {
                        bgcolor: theme.palette.action.hover,
                      },
                    }}
                  />
                ))}
              </RadioGroup>

              {/* Submit button */}
              <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
                {currentTestSession?.mode === 'practice' && (
                  <Button
                    variant="outlined"
                    color="primary"
                    disabled={loading}
                    onClick={() => setConfirmAbortDialog(true)}
                  >
                    Exit Test
                  </Button>
                )}
                <Box sx={{ marginLeft: 'auto' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    disabled={loading || !currentAnswer}
                    onClick={handleSubmit}
                    endIcon={loading ? <CircularProgress size={20} /> : <ArrowForward />}
                  >
                    {loading ? 'Submitting...' : 'Submit Answer'}
                  </Button>
                </Box>
              </Box>
            </Box>
          )}

          {/* Last Question Feedback (for practice mode) */}
          {currentTestSession?.mode === 'practice' && lastAnswer && (
            <Box sx={{ mt: 4 }}>
              <Divider sx={{ mb: 3 }} />
              <Typography variant="h6" gutterBottom>
                Previous Question Feedback
              </Typography>

              <Alert
                severity={lastAnswer.isCorrect ? 'success' : 'error'}
                icon={lastAnswer.isCorrect ? <CheckCircle /> : <Cancel />}
                sx={{ mb: 2 }}
              >
                {lastAnswer.isCorrect ? (
                  <Typography variant="body2">
                    Your answer was correct! Well done.
                  </Typography>
                ) : (
                  <Typography variant="body2">
                    Your answer was incorrect. The correct answer was <strong>{lastAnswer.correctAnswer}</strong>.
                  </Typography>
                )}
              </Alert>

              <Box sx={{
                p: 2,
                bgcolor: theme.palette.background.default,
                borderRadius: 1,
                mt: 2
              }}>
                <Typography variant="subtitle2" gutterBottom>
                  Explanation:
                </Typography>
                <Typography variant="body2">
                  {lastAnswer.explanation}
                </Typography>
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Completed Dialog */}
      <Dialog
        open={dialogOpen}
        aria-labelledby="completed-dialog-title"
      >
        <DialogTitle id="completed-dialog-title">
          Test Completed
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            You've completed all questions in this test session. Would you like to view your results?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => navigate('/app/test-prep')}>
            Back to Dashboard
          </Button>
          <Button onClick={handleViewResults} variant="contained" color="primary" autoFocus>
            View Results
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirm Abort Dialog */}
      <Dialog
        open={confirmAbortDialog}
        onClose={() => setConfirmAbortDialog(false)}
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Warning color="warning" sx={{ mr: 1 }} />
            Abort Test Session?
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to quit this test? Your progress will be saved, but the test will be marked as abandoned.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmAbortDialog(false)}>
            Continue Test
          </Button>
          <Button onClick={handleAbortTest} color="error">
            Quit Test
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TestSession;