const { initializeJobApplicationAgent, runJobApplicationAgent } = require('./langchainAgent');
const { JobPlatformRegistry } = require('./jobPlatformRegistry');
const { BrowserManager } = require('./browserManager');
const { SemanticKernelPlanner } = require('./semanticKernelPlanner');
const fs = require('fs').promises;
const path = require('path');

// Import database models
const AutonomousJob = require('../../models/autonomousJobModel');
const User = require('../../models/userModel');
const Resume = require('../../models/resumeModel');

class JobApplicationAgentService {
  constructor() {
    this.jobPlatformRegistry = new JobPlatformRegistry();
    this.semanticKernelPlanner = new SemanticKernelPlanner();
    this.runningAgents = new Map(); // Map of userId -> running agent info
  }

  /**
   * Start a job application campaign
   * @param {Object} campaign - The campaign configuration
   * @param {Object} user - The user information
   * @param {Array} resumes - The user's resumes
   * @returns {Promise<Object>} - The started campaign
   */
  async startCampaign(campaign, user, resumes) {
    try {
      // Initialize user data
      const userProfile = this.buildUserProfile(campaign, user);
      
      // Get primary resume
      const primaryResume = this.getPrimaryResume(campaign, resumes);
      
      if (!primaryResume) {
        throw new Error('No valid resume found for the campaign');
      }
      
      // Initialize browser with headless mode based on user preference
      const browserManager = new BrowserManager({
        headless: !campaign.viewApplicationSubmission, // If viewApplicationSubmission is true, headless is false
        browserType: 'puppeteer'
      });
      
      // Initialize agent
      const agentConfig = {
        headless: !campaign.viewApplicationSubmission,
        maxApplicationsPerDay: campaign.agentSettings.applicationFrequency,
        campaignDuration: campaign.agentSettings.campaignDuration,
        prioritySettings: campaign.agentSettings.prioritySettings,
        excludedCompanies: campaign.jobSearchParameters.whereNotToApply.blacklistedCompanies,
        excludeCurrentEmployer: campaign.jobSearchParameters.whereNotToApply.excludeCurrentEmployer,
        excludePreviouslyApplied: campaign.jobSearchParameters.whereNotToApply.excludePreviouslyApplied,
        targetCompanies: campaign.jobSearchParameters.whereToApply.targetCompanies,
        jobBoards: campaign.jobSearchParameters.whereToApply.jobBoards,
        desiredLocations: campaign.jobSearchParameters.geographicPreferences.desiredLocations,
        remoteWorkOption: campaign.jobSearchParameters.geographicPreferences.remoteWorkOption,
        applicationCustomization: campaign.applicationCustomization,
        approvalWorkflow: campaign.approvalWorkflow
      };
      
      const { agent } = await initializeJobApplicationAgent(agentConfig, userProfile, primaryResume);
      
      // Store the agent in the running agents map
      this.runningAgents.set(user._id.toString(), {
        agent,
        browserManager,
        campaign,
        userProfile,
        primaryResume,
        status: 'active',
        startTime: Date.now(),
        lastRunTime: Date.now(),
        applications: []
      });
      
      return {
        success: true,
        message: 'Campaign started successfully',
        campaignId: campaign._id
      };
    } catch (error) {
      console.error('Error starting campaign:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Load an active campaign from the database and initialize the agent
   * @param {string} userId - The user ID
   * @returns {Promise<Object|null>} - The loaded campaign and agent info, or null if not found
   */
  async loadActiveCampaignFromDatabase(userId) {
    try {
      console.log(`Loading active campaign for user: ${userId}`);

      // Find active campaign for the user
      const campaign = await AutonomousJob.findOne({
        user: userId,
        status: 'active'
      }).populate('user');

      if (!campaign) {
        console.log(`No active campaign found in database for user: ${userId}`);
        return null;
      }

      console.log(`Found active campaign: ${campaign._id}`);

      // Get user profile
      const user = campaign.user;
      if (!user) {
        console.error('Campaign user not found');
        return null;
      }

      // Get primary resume
      const primaryResume = await Resume.findOne({
        user: userId,
        isPrimary: true
      });

      if (!primaryResume) {
        console.error('No primary resume found for user');
        return null;
      }

      // Create user profile object
      const userProfile = {
        userId: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: campaign.personalDetails.phone,
        currentLocation: campaign.personalDetails.currentLocation,
        desiredJobTitles: campaign.personalDetails.desiredJobTitles,
        experienceLevel: campaign.personalDetails.experienceLevel
      };

      // Initialize browser manager
      const browserManager = new BrowserManager({
        headless: true // Default to headless for auto-apply
      });

      // Create agent configuration
      const agentConfig = {
        headless: true,
        desiredJobTitles: campaign.personalDetails.desiredJobTitles,
        desiredLocations: campaign.jobSearchParameters.geographicPreferences.desiredLocations,
        remoteWorkOption: campaign.jobSearchParameters.geographicPreferences.remoteWorkOption,
        applicationCustomization: campaign.applicationCustomization,
        approvalWorkflow: campaign.approvalWorkflow
      };

      // Initialize the agent
      const { agent } = await initializeJobApplicationAgent(agentConfig, userProfile, primaryResume);

      // Store the agent in the running agents map
      const agentInfo = {
        agent,
        browserManager,
        campaign,
        userProfile,
        primaryResume,
        status: 'active',
        startTime: Date.now(),
        lastRunTime: Date.now(),
        applications: []
      };

      this.runningAgents.set(userId, agentInfo);

      console.log(`Successfully loaded and initialized campaign for user: ${userId}`);
      return agentInfo;

    } catch (error) {
      console.error('Error loading active campaign from database:', error);
      return null;
    }
  }

  /**
   * Process job applications for a user's campaign
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - The result of the processing
   */
  async processApplications(userId) {
    try {
      let agentInfo = this.runningAgents.get(userId);

      // If no running agent found, try to load from database
      if (!agentInfo || agentInfo.status !== 'active') {
        console.log(`No running agent found for user ${userId}, attempting to load from database...`);
        agentInfo = await this.loadActiveCampaignFromDatabase(userId);

        if (!agentInfo) {
          return {
            success: false,
            message: 'No active campaign found for this user. Please create and start a campaign first.'
          };
        }
      }
      
      // Update last run time
      agentInfo.lastRunTime = Date.now();

      // Update campaign's lastRunDate in database
      try {
        await AutonomousJob.findByIdAndUpdate(agentInfo.campaign._id, {
          lastRunDate: new Date()
        });
      } catch (dbError) {
        console.warn('Failed to update campaign lastRunDate:', dbError.message);
      }
      
      // Get job search parameters
      const searchParams = {
        keywords: agentInfo.campaign.personalDetails.desiredJobTitles.join(' OR '),
        location: agentInfo.campaign.personalDetails.currentLocation,
        platforms: agentInfo.campaign.jobSearchParameters.whereToApply.jobBoards
      };
      
      // Search for jobs
      const jobResults = await this.jobPlatformRegistry.searchJobs(searchParams);
      
      if (!jobResults || jobResults.length === 0) {
        return {
          success: false,
          message: 'No jobs found matching search criteria'
        };
      }
      
      // Process each job search result sequentially to avoid navigation issues
      const applications = [];
      const errors = [];
      
      for (const jobResult of jobResults) {
        try {
          // Check application limits
          if (applications.length >= agentInfo.campaign.agentSettings.applicationFrequency) {
            console.log(`Reached daily application limit of ${agentInfo.campaign.agentSettings.applicationFrequency}`);
            break; // Reached daily limit
          }
          
          console.log(`Processing job ${applications.length + 1}/${Math.min(jobResults.length, agentInfo.campaign.agentSettings.applicationFrequency)}: ${jobResult.title} at ${jobResult.company}`);
          
          // Process the job with enhanced error handling
          const applicationResult = await this.processJobWithRetry(jobResult, agentInfo);
          
          if (applicationResult.success && applicationResult.submissionConfirmed) {
            applications.push(applicationResult);
            console.log(`✅ Successfully applied to: ${jobResult.title} at ${jobResult.company}`);
            
            // Add delay between successful applications to avoid being flagged
            if (applications.length < agentInfo.campaign.agentSettings.applicationFrequency) {
              console.log('Waiting 30 seconds before next application...');
              await this.delay(30000);
            }
          } else {
            errors.push(applicationResult);
            console.log(`❌ Failed to apply to: ${jobResult.title} at ${jobResult.company} - ${applicationResult.error || applicationResult.reason}`);
            
            // Add shorter delay between failed attempts
            await this.delay(10000);
          }
        } catch (jobError) {
          console.error('Error processing job:', jobError);
          errors.push({
            success: false,
            error: jobError.message,
            job: jobResult,
            submissionConfirmed: false
          });
          
          // Add delay after errors
          await this.delay(10000);
        }
      }
      
      // Update campaign statistics
      await this.updateCampaignStatistics(agentInfo.campaign, applications);
      
      // Store application results
      agentInfo.applications = [...agentInfo.applications, ...applications];
      
      return {
        success: true,
        applications,
        errors,
        message: `Processed ${applications.length} applications with ${errors.length} errors`
      };
    } catch (error) {
      console.error('Error processing applications:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process a specific job
   * @param {Object} job - The job data
   * @param {Object} agentInfo - The agent information
   * @returns {Promise<Object>} - The result of the processing
   */
  async processJob(job, agentInfo) {
    try {
      // Create job application plan using Semantic Kernel
      const plan = await this.semanticKernelPlanner.createPlan(job, agentInfo.userProfile, agentInfo.primaryResume);
      
      if (!plan.success) {
        return {
          success: false,
          error: plan.error,
          job
        };
      }
      
      // Check if job match score is acceptable
      if (plan.jobMatchScore && plan.jobMatchScore.score < 50) {
        return {
          success: false,
          reason: 'Low job match score',
          score: plan.jobMatchScore.score,
          explanation: plan.jobMatchScore.explanation,
          job
        };
      }
      
      // Check if approval is needed based on workflow settings
      if (agentInfo.campaign.approvalWorkflow !== 'full-autonomy') {
        const requiresApproval = this.checkIfApprovalRequired(agentInfo.campaign, plan.jobMatchScore.score);
        
        if (requiresApproval) {
          // Store job for later approval
          this.storeJobForApproval(agentInfo.campaign._id, job, plan);
          
          return {
            success: false,
            reason: 'Requires manual approval',
            job,
            plan
          };
        }
      }
      
      // Run the agent to submit the application
      const result = await runJobApplicationAgent(agentInfo.agent, job, agentInfo.userProfile, agentInfo.primaryResume);
      
      // Validate the submission result
      const submissionValidated = this.validateSubmissionResult(result);
      
      if (!submissionValidated.success) {
        return {
          success: false,
          error: submissionValidated.error,
          reason: submissionValidated.reason,
          job,
          result: submissionValidated.details
        };
      }
      
      // Take a screenshot if available
      let screenshotPath = null;
      if (agentInfo.browserManager && agentInfo.browserManager.isInitialized) {
        const screenshotsDir = path.join(__dirname, '..', '..', 'uploads', 'screenshots');
        
        // Ensure directory exists
        await fs.mkdir(screenshotsDir, { recursive: true });
        
        // Take screenshot
        screenshotPath = path.join(screenshotsDir, `application-${Date.now()}.png`);
        await agentInfo.browserManager.takeScreenshot(screenshotPath);
      }

      // Emit Kafka event for successful application
      try {
        await this.emitApplicationSuccessEvent({
          userId: agentInfo.userProfile.userId,
          campaignId: agentInfo.campaign._id,
          job,
          matchScore: plan.jobMatchScore.score,
          submissionDetails: submissionValidated.details
        });
      } catch (kafkaError) {
        console.warn('Failed to emit Kafka event:', kafkaError.message);
        // Don't fail the whole process for Kafka errors
      }
      
      return {
        success: true,
        job,
        result: submissionValidated.details,
        screenshotPath,
        appliedAt: new Date(),
        matchScore: plan.jobMatchScore.score,
        submissionConfirmed: true
      };
    } catch (error) {
      console.error('Error processing job:', error);
      return {
        success: false,
        error: error.message,
        job
      };
    }
  }

  /**
   * Process a job with retry logic for enhanced reliability
   * @param {Object} job - The job data
   * @param {Object} agentInfo - The agent information
   * @param {number} maxRetries - Maximum number of retry attempts (default: 2)
   * @returns {Promise<Object>} - The result of the processing
   */
  async processJobWithRetry(job, agentInfo, maxRetries = 2) {
    let lastError = null;
    
    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        console.log(`Processing job attempt ${attempt}/${maxRetries + 1}: ${job.title} at ${job.company}`);
        
        const result = await this.processJob(job, agentInfo);
        
        // If successful, return immediately
        if (result.success && result.submissionConfirmed) {
          console.log(`✅ Job application successful on attempt ${attempt}`);
          return result;
        }
        
        // If not successful but not the last attempt, prepare for retry
        if (attempt <= maxRetries) {
          console.log(`⚠️ Attempt ${attempt} failed: ${result.error || result.reason}. Retrying...`);
          lastError = result;
          
          // Wait before retry with exponential backoff
          const retryDelay = Math.min(5000 * Math.pow(2, attempt - 1), 30000); // 5s, 10s, 20s, max 30s
          console.log(`Waiting ${retryDelay / 1000} seconds before retry...`);
          await this.delay(retryDelay);
          
          // Reset browser state if needed
          if (agentInfo.browserManager && agentInfo.browserManager.isInitialized) {
            try {
              await agentInfo.browserManager.reset();
            } catch (resetError) {
              console.warn('Failed to reset browser state:', resetError.message);
            }
          }
        } else {
          // Last attempt failed, return the result
          console.log(`❌ All ${maxRetries + 1} attempts failed for job: ${job.title} at ${job.company}`);
          return result;
        }
        
      } catch (error) {
        console.error(`Error in job processing attempt ${attempt}:`, error);
        lastError = {
          success: false,
          error: error.message,
          job,
          submissionConfirmed: false
        };
        
        if (attempt <= maxRetries) {
          const retryDelay = Math.min(5000 * Math.pow(2, attempt - 1), 30000);
          console.log(`Waiting ${retryDelay / 1000} seconds before retry...`);
          await this.delay(retryDelay);
        }
      }
    }
    
    // If we get here, all retries failed
    return lastError || {
      success: false,
      error: 'All retry attempts failed',
      job,
      submissionConfirmed: false
    };
  }

  /**
   * Simple delay utility function
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise<void>}
   */
  async delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate the submission result from the agent
   * @param {Object} result - The result from runJobApplicationAgent
   * @returns {Object} - Validation result
   */
  validateSubmissionResult(result) {
    try {
      // Check if the result contains submission information
      if (!result || typeof result !== 'object') {
        return {
          success: false,
          error: 'Invalid result format from agent',
          reason: 'agent_error'
        };
      }

      // Look for submission confirmation in the result
      const submissionResults = this.extractSubmissionResults(result);
      
      if (submissionResults.length === 0) {
        return {
          success: false,
          error: 'No submission attempts found in agent result',
          reason: 'no_submission_attempts'
        };
      }

      // Check if any submission was successful
      const successfulSubmissions = submissionResults.filter(sub => sub.success);
      
      if (successfulSubmissions.length === 0) {
        const lastSubmission = submissionResults[submissionResults.length - 1];
        return {
          success: false,
          error: `Form submission failed: ${lastSubmission.message || 'Unknown error'}`,
          reason: 'submission_failed',
          details: lastSubmission
        };
      }

      // Return the most recent successful submission
      const latestSuccess = successfulSubmissions[successfulSubmissions.length - 1];
      return {
        success: true,
        details: latestSuccess
      };

    } catch (error) {
      console.error('Error validating submission result:', error);
      return {
        success: false,
        error: `Validation error: ${error.message}`,
        reason: 'validation_error'
      };
    }
  }

  /**
   * Extract submission results from agent output
   * @param {Object} result - Agent result object
   * @returns {Array} - Array of submission results
   */
  extractSubmissionResults(result) {
    const submissions = [];
    
    try {
      // Check if result contains direct submission info
      if (result.success !== undefined) {
        submissions.push(result);
        return submissions;
      }

      // Look for submission results in nested objects or arrays
      const searchForSubmissions = (obj, path = '') => {
        if (typeof obj !== 'object' || obj === null) return;
        
        for (const [key, value] of Object.entries(obj)) {
          const currentPath = path ? `${path}.${key}` : key;
          
          // Look for submission-related keys
          if (key.includes('submit') || key.includes('application') || key.includes('form')) {
            if (typeof value === 'object' && value.success !== undefined) {
              submissions.push({
                ...value,
                path: currentPath
              });
            }
          }
          
          // Recursively search nested objects
          if (typeof value === 'object') {
            searchForSubmissions(value, currentPath);
          }
        }
      };

      searchForSubmissions(result);
      
      // If still no submissions found, look for any object with success/error pattern
      if (submissions.length === 0) {
        const findSuccessObjects = (obj) => {
          if (typeof obj !== 'object' || obj === null) return;
          
          if (obj.hasOwnProperty('success') && typeof obj.success === 'boolean') {
            submissions.push(obj);
          }
          
          for (const value of Object.values(obj)) {
            if (typeof value === 'object') {
              findSuccessObjects(value);
            }
          }
        };

        findSuccessObjects(result);
      }

    } catch (error) {
      console.error('Error extracting submission results:', error);
    }

    return submissions;
  }

  /**
   * Emit Kafka event for successful application submission
   * @param {Object} eventData - Event data
   * @returns {Promise<void>}
   */
  async emitApplicationSuccessEvent(eventData) {
    try {
      // Import Kafka service (assuming it exists)
      const kafkaService = require('../kafkaService');
      
      const event = {
        eventType: 'ApplicationSent',
        timestamp: new Date().toISOString(),
        userId: eventData.userId,
        campaignId: eventData.campaignId,
        jobDetails: {
          title: eventData.job.title || eventData.job.jobTitle,
          company: eventData.job.company,
          location: eventData.job.location,
          url: eventData.job.url
        },
        matchScore: eventData.matchScore,
        submissionDetails: eventData.submissionDetails,
        metadata: {
          platform: 'AutoApplyAgent',
          version: '2.0.0'
        }
      };

      await kafkaService.publishEvent('job-applications', event);
      console.log('Successfully emitted ApplicationSent event for job:', eventData.job.title);
      
    } catch (error) {
      console.error('Failed to emit ApplicationSent event:', error);
      throw error;
    }
  }

  /**
   * Check if manual approval is required for a job
   * @param {Object} campaign - The campaign configuration
   * @param {number} matchScore - The job match score
   * @returns {boolean} - Whether approval is required
   */
  checkIfApprovalRequired(campaign, matchScore) {
    switch (campaign.approvalWorkflow) {
      case 'full-autonomy':
        return false;
      case 'semi-autonomous':
        return matchScore < 70; // Require approval for jobs with match score below 70
      case 'high-value-approval':
        // Require approval for high-value companies or high match scores
        const isHighValueCompany = (job) => {
          return campaign.jobSearchParameters.whereToApply.targetCompanies.includes(job.company);
        };
        return matchScore > 80 || isHighValueCompany(job);
      default:
        return true;
    }
  }

  /**
   * Store a job for manual approval
   * @param {string} campaignId - The campaign ID
   * @param {Object} job - The job data
   * @param {Object} plan - The application plan
   */
  async storeJobForApproval(campaignId, job, plan) {
    // Implementation to store jobs requiring approval
    // This would typically save to a database collection
    console.log(`Storing job for approval - Campaign: ${campaignId}, Job: ${job.title} at ${job.company}`);
  }

  /**
   * Update campaign statistics based on application results
   * @param {Object} campaign - The campaign object
   * @param {Array} applications - Array of successful applications
   */
  async updateCampaignStatistics(campaign, applications) {
    try {
      if (!applications || applications.length === 0) {
        return;
      }

      // Calculate statistics
      const totalApplications = applications.length;
      const totalMatchScore = applications.reduce((sum, app) => sum + (app.matchScore || 0), 0);
      const averageMatchScore = totalApplications > 0 ? totalMatchScore / totalApplications : 0;

      // Update campaign statistics in database
      await AutonomousJob.findByIdAndUpdate(campaign._id, {
        $inc: {
          'statistics.totalApplicationsSubmitted': totalApplications
        },
        $set: {
          'statistics.averageMatchScore': averageMatchScore,
          lastRunDate: new Date()
        }
      });

      console.log(`Updated campaign statistics: ${totalApplications} applications, avg match score: ${averageMatchScore.toFixed(1)}%`);

    } catch (error) {
      console.error('Error updating campaign statistics:', error);
    }
  }



  /**
   * Build a user profile from campaign and user data
   * @param {Object} campaign - The campaign configuration
   * @param {Object} user - The user information
   * @returns {Object} - The user profile
   */
  buildUserProfile(campaign, user) {
    return {
      firstName: user.firstName || campaign.personalDetails.fullName.split(' ')[0],
      lastName: user.lastName || campaign.personalDetails.fullName.split(' ').slice(1).join(' '),
      email: user.email || campaign.personalDetails.email,
      phone: user.phone || campaign.personalDetails.phone,
      location: campaign.personalDetails.currentLocation,
      desiredJobTitles: campaign.personalDetails.desiredJobTitles,
      experienceLevel: campaign.personalDetails.experienceLevel,
      desiredSalaryRange: campaign.personalDetails.desiredSalaryRange,
      availabilityDate: campaign.personalDetails.startDateAvailability,
      currentEmployer: user.currentEmployer,
      linkedinProfile: user.socialProfiles?.linkedin,
      githubProfile: user.socialProfiles?.github,
      portfolioWebsite: user.socialProfiles?.portfolio
    };
  }

  /**
   * Get the primary resume for a campaign
   * @param {Object} campaign - The campaign configuration
   * @param {Array} resumes - The user's resumes
   * @returns {Object|null} - The primary resume or null if not found
   */
  getPrimaryResume(campaign, resumes) {
    if (!campaign.resumes || campaign.resumes.length === 0) {
      // Return first resume if no specific resume is selected
      return resumes[0] || null;
    }
    
    // Get first resume ID from campaign
    const primaryResumeId = campaign.resumes[0]?.resumeId;
    
    if (!primaryResumeId) {
      return resumes[0] || null;
    }
    
    // Find matching resume
    const primaryResume = resumes.find(resume => 
      resume._id.toString() === primaryResumeId.toString()
    );
    
    return primaryResume || resumes[0] || null;
  }

  /**
   * Pause a running campaign
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - The result of the pause operation
   */
  async pauseCampaign(userId) {
    try {
      const agentInfo = this.runningAgents.get(userId);
      
      if (!agentInfo) {
        return {
          success: false,
          message: 'No campaign found for this user'
        };
      }
      
      // Update status
      agentInfo.status = 'paused';
      
      return {
        success: true,
        message: 'Campaign paused successfully'
      };
    } catch (error) {
      console.error('Error pausing campaign:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Resume a paused campaign
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - The result of the resume operation
   */
  async resumeCampaign(userId) {
    try {
      const agentInfo = this.runningAgents.get(userId);
      
      if (!agentInfo) {
        return {
          success: false,
          message: 'No campaign found for this user'
        };
      }
      
      // Update status
      agentInfo.status = 'active';
      agentInfo.lastRunTime = Date.now();
      
      return {
        success: true,
        message: 'Campaign resumed successfully'
      };
    } catch (error) {
      console.error('Error resuming campaign:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Cancel a running campaign
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - The result of the cancel operation
   */
  async cancelCampaign(userId) {
    try {
      const agentInfo = this.runningAgents.get(userId);
      
      if (!agentInfo) {
        return {
          success: false,
          message: 'No campaign found for this user'
        };
      }
      
      // Close browser if open
      if (agentInfo.browserManager && agentInfo.browserManager.isInitialized) {
        await agentInfo.browserManager.close();
      }
      
      // Remove from running agents
      this.runningAgents.delete(userId);
      
      return {
        success: true,
        message: 'Campaign cancelled successfully'
      };
    } catch (error) {
      console.error('Error cancelling campaign:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get applications for a campaign
   * @param {string} userId - The user ID
   * @returns {Promise<Object>} - The campaign applications
   */
  async getCampaignApplications(userId) {
    try {
      const agentInfo = this.runningAgents.get(userId);
      
      if (!agentInfo) {
        return {
          success: false,
          message: 'No campaign found for this user'
        };
      }
      
      return {
        success: true,
        applications: agentInfo.applications
      };
    } catch (error) {
      console.error('Error getting campaign applications:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update a running campaign configuration
   * @param {string} userId - The user ID
   * @param {Object} updatedConfig - The updated configuration
   * @returns {Promise<Object>} - The result of the update operation
   */
  async updateCampaignConfig(userId, updatedConfig) {
    try {
      const agentInfo = this.runningAgents.get(userId);
      
      if (!agentInfo) {
        return {
          success: false,
          message: 'No campaign found for this user'
        };
      }
      
      // Update configuration
      Object.assign(agentInfo.campaign, updatedConfig);
      
      // Update agent configuration
      agentInfo.agent.config = {
        ...agentInfo.agent.config,
        maxApplicationsPerDay: agentInfo.campaign.agentSettings.applicationFrequency,
        prioritySettings: agentInfo.campaign.agentSettings.prioritySettings,
        // Update other relevant config properties
      };
      
      return {
        success: true,
        message: 'Campaign configuration updated successfully'
      };
    } catch (error) {
      console.error('Error updating campaign configuration:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Toggle headless mode for a running campaign
   * @param {string} userId - The user ID
   * @param {boolean} showBrowser - Whether to show the browser
   * @returns {Promise<Object>} - The result of the toggle operation
   */
  async toggleHeadlessMode(userId, showBrowser) {
    try {
      const agentInfo = this.runningAgents.get(userId);
      
      if (!agentInfo) {
        return {
          success: false,
          message: 'No campaign found for this user'
        };
      }
      
      // Close existing browser if open
      if (agentInfo.browserManager && agentInfo.browserManager.isInitialized) {
        await agentInfo.browserManager.close();
      }
      
      // Create new browser manager with updated headless setting
      agentInfo.browserManager = new BrowserManager({
        headless: !showBrowser,
        browserType: 'puppeteer'
      });
      
      // Update agent configuration
      agentInfo.agent.config = {
        ...agentInfo.agent.config,
        headless: !showBrowser
      };
      
      return {
        success: true,
        message: `Browser visibility ${showBrowser ? 'enabled' : 'disabled'} successfully`
      };
    } catch (error) {
      console.error('Error toggling headless mode:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = { JobApplicationAgentService };
