/**
 * Test script to verify auto-apply functionality
 */

const mongoose = require('mongoose');
const { JobApplicationAgentService } = require('./services/agentic/jobApplicationAgent');

// Connect to MongoDB
async function connectDB() {
  try {
    await mongoose.connect('mongodb://localhost:27017/job-application-platform');
    console.log('✅ Connected to MongoDB');
    return true;
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    return false;
  }
}

// Test auto-apply functionality
async function testAutoApply() {
  console.log('🚀 Testing Auto-Apply Functionality\n');
  
  // Connect to database
  const connected = await connectDB();
  if (!connected) {
    return;
  }
  
  try {
    // Initialize the service
    const service = new JobApplicationAgentService();
    
    // Test with a user who has an active campaign
    const testUserId = '507f1f77bcf86cd799439011';
    console.log(`Testing with user ID: ${testUserId}`);
    
    // Test 1: Check if we can load the campaign from database
    console.log('\n📋 Test 1: Loading active campaign from database...');
    const agentInfo = await service.loadActiveCampaignFromDatabase(testUserId);
    
    if (agentInfo) {
      console.log('✅ Successfully loaded campaign from database');
      console.log(`   Campaign ID: ${agentInfo.campaign._id}`);
      console.log(`   Campaign Status: ${agentInfo.campaign.status}`);
      console.log(`   User: ${agentInfo.userProfile.firstName} ${agentInfo.userProfile.lastName}`);
      console.log(`   Desired Job Titles: ${agentInfo.campaign.personalDetails.desiredJobTitles.join(', ')}`);
      console.log(`   Application Frequency: ${agentInfo.campaign.agentSettings.applicationFrequency} per day`);
    } else {
      console.log('❌ Failed to load campaign from database');
      return;
    }
    
    // Test 2: Test the processApplications method
    console.log('\n🔄 Test 2: Testing processApplications method...');
    const result = await service.processApplications(testUserId);
    
    console.log('\n📊 Results:');
    console.log(`   Success: ${result.success}`);
    console.log(`   Message: ${result.message}`);
    
    if (result.success) {
      console.log(`   Applications Processed: ${result.applications?.length || 0}`);
      console.log(`   Errors: ${result.errors?.length || 0}`);
      
      if (result.applications && result.applications.length > 0) {
        console.log('\n📝 Application Details:');
        result.applications.forEach((app, index) => {
          console.log(`   ${index + 1}. ${app.job?.title || 'Unknown Job'} at ${app.job?.company || 'Unknown Company'}`);
          console.log(`      Match Score: ${app.matchScore || 'N/A'}%`);
          console.log(`      Status: ${app.success ? 'Success' : 'Failed'}`);
        });
      }
      
      if (result.errors && result.errors.length > 0) {
        console.log('\n❌ Errors:');
        result.errors.forEach((error, index) => {
          console.log(`   ${index + 1}. ${error.error || error.reason || 'Unknown error'}`);
        });
      }
    } else {
      console.log(`   Error: ${result.error || 'Unknown error'}`);
    }
    
    // Test 3: Check campaign statistics
    console.log('\n📈 Test 3: Checking campaign statistics...');
    const AutonomousJob = require('./models/autonomousJobModel');
    const campaign = await AutonomousJob.findOne({
      user: testUserId,
      status: 'active'
    });
    
    if (campaign) {
      console.log('✅ Campaign statistics:');
      console.log(`   Total Applications Submitted: ${campaign.statistics.totalApplicationsSubmitted}`);
      console.log(`   Average Match Score: ${campaign.statistics.averageMatchScore?.toFixed(1) || 0}%`);
      console.log(`   Last Run Date: ${campaign.lastRunDate || 'Never'}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Close database connection
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the test
testAutoApply().then(() => {
  console.log('\n✅ Test completed');
  process.exit(0);
}).catch(error => {
  console.error('\n❌ Test failed:', error);
  process.exit(1);
});
