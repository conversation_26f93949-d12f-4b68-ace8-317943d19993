const { JobApplicationAgentService } = require('../services/agentic/jobApplicationAgent');
const AutonomousJob = require('../models/autonomousJobModel');
const EnterpriseResumeBuilder = require('../services/enterprise-resume-builder');
const Application = require('../models/applicationModel');
const User = require('../models/userModel');
const Resume = require('../models/resumeModel');
const catchAsync = require('../utils/catchAsync');
const AppError = require('../utils/appError');
const schedulerService = require('../services/scheduler.service');

// Initialize the job application agent service
const jobApplicationAgentService = new JobApplicationAgentService();

// Start a job application campaign with agentic capabilities
exports.startAgenticCampaign = catchAsync(async (req, res, next) => {
  // Get the autonomous job campaign
  const campaign = await AutonomousJob.findById(req.params.id);

  if (!campaign) {
    return next(new AppError('No campaign found with that ID', 404));
  }

  // Check if campaign belongs to the current user
  if (campaign.user.toString() !== req.user.id) {
    return next(new AppError('You do not have permission to start this campaign', 403));
  }

  // Get user details and resumes
  const user = await User.findById(req.user.id);
  const resumes = await Resume.find({ user: req.user.id });

  // Add request body options
  campaign.viewApplicationSubmission = req.body.viewApplicationSubmission || false;

  // Start the campaign
  const result = await jobApplicationAgentService.startCampaign(campaign, user, resumes);

  if (!result.success) {
    return next(new AppError(result.error || 'Failed to start campaign', 400));
  }

  // Update campaign status in the database
  campaign.status = 'active';
  campaign.startDate = Date.now();
  campaign.lastRunDate = Date.now();

  // Calculate end date based on campaign duration
  const endDate = new Date();
  endDate.setDate(endDate.getDate() + campaign.agentSettings.campaignDuration);
  campaign.endDate = endDate;

  await campaign.save();

  // Trigger immediate processing of the campaign
  // This is done asynchronously to avoid blocking the response
  setTimeout(async () => {
    try {
      console.log(`Triggering immediate processing for campaign ${campaign._id}`);
      const processingResult = await schedulerService.processSpecificCampaign(campaign._id, req.user.id);
      console.log(`Immediate processing result:`, processingResult);
    } catch (processingError) {
      console.error(`Error during immediate processing of campaign ${campaign._id}:`, processingError);
    }
  }, 1000);

  res.status(200).json({
    status: 'success',
    message: 'Campaign started successfully',
    data: {
      campaign
    }
  });
});

// Process applications for a campaign
exports.processApplications = catchAsync(async (req, res, next) => {
  const userId = req.user.id;

  // Find active campaign for user
  const campaign = await AutonomousJob.findOne({
    user: userId,
    status: 'active'
  });

  if (!campaign) {
    return next(new AppError('No active campaign found', 404));
  }

  // Process applications
  const result = await jobApplicationAgentService.processApplications(userId);

  if (!result.success) {
    return next(new AppError(result.message || 'Failed to process applications', 400));
  }

  // Update campaign last run date
  campaign.lastRunDate = Date.now();
  await campaign.save();

  // Create application records in database
  if (result.applications && result.applications.length > 0) {
    const applicationPromises = result.applications
      .filter(app => app.success)
      .map(app => {
        return Application.create({
          user: userId,
          autonomousJobCampaign: campaign._id,
          jobTitle: app.job.jobTitle || app.job.title,
          company: app.job.company,
          location: app.job.location,
          jobDescription: app.job.description,
          jobUrl: app.job.url,
          status: 'applied',
          appliedDate: app.appliedAt || Date.now(),
          matchScore: app.matchScore || 0,
          screenshotPath: app.screenshotPath,
          notes: `Applied automatically through campaign. Match score: ${app.matchScore || 0}%`
        });
      });

    await Promise.all(applicationPromises);
  }

  res.status(200).json({
    status: 'success',
    message: `Processed ${result.applications?.length || 0} applications with ${result.errors?.length || 0} errors`,
    data: result
  });
});

// Pause a running campaign
exports.pauseAgenticCampaign = catchAsync(async (req, res, next) => {
  // Get the autonomous job campaign
  const campaign = await AutonomousJob.findById(req.params.id);

  if (!campaign) {
    return next(new AppError('No campaign found with that ID', 404));
  }

  // Check if campaign belongs to the current user
  if (campaign.user.toString() !== req.user.id) {
    return next(new AppError('You do not have permission to pause this campaign', 403));
  }

  // Pause the campaign
  const result = await jobApplicationAgentService.pauseCampaign(req.user.id);

  if (!result.success) {
    return next(new AppError(result.error || 'Failed to pause campaign', 400));
  }

  // Update campaign status in the database
  campaign.status = 'paused';
  await campaign.save();

  res.status(200).json({
    status: 'success',
    message: 'Campaign paused successfully',
    data: {
      campaign
    }
  });
});

// Resume a paused campaign
exports.resumeAgenticCampaign = catchAsync(async (req, res, next) => {
  // Get the autonomous job campaign
  const campaign = await AutonomousJob.findById(req.params.id);

  if (!campaign) {
    return next(new AppError('No campaign found with that ID', 404));
  }

  // Check if campaign belongs to the current user
  if (campaign.user.toString() !== req.user.id) {
    return next(new AppError('You do not have permission to resume this campaign', 403));
  }

  // Resume the campaign
  const result = await jobApplicationAgentService.resumeCampaign(req.user.id);

  if (!result.success) {
    return next(new AppError(result.error || 'Failed to resume campaign', 400));
  }

  // Update campaign status in the database
  campaign.status = 'active';
  campaign.lastRunDate = Date.now();
  await campaign.save();

  res.status(200).json({
    status: 'success',
    message: 'Campaign resumed successfully',
    data: {
      campaign
    }
  });
});

// Cancel a running campaign
exports.cancelAgenticCampaign = catchAsync(async (req, res, next) => {
  // Get the autonomous job campaign
  const campaign = await AutonomousJob.findById(req.params.id);

  if (!campaign) {
    return next(new AppError('No campaign found with that ID', 404));
  }

  // Check if campaign belongs to the current user
  if (campaign.user.toString() !== req.user.id) {
    return next(new AppError('You do not have permission to cancel this campaign', 403));
  }

  // Cancel the campaign
  const result = await jobApplicationAgentService.cancelCampaign(req.user.id);

  if (!result.success) {
    return next(new AppError(result.error || 'Failed to cancel campaign', 400));
  }

  // Update campaign status in the database
  campaign.status = 'cancelled';
  await campaign.save();

  res.status(200).json({
    status: 'success',
    message: 'Campaign cancelled successfully',
    data: {
      campaign
    }
  });
});

// Toggle headless mode
exports.toggleHeadlessMode = catchAsync(async (req, res, next) => {
  const { showBrowser } = req.body;

  if (typeof showBrowser !== 'boolean') {
    return next(new AppError('showBrowser parameter must be a boolean', 400));
  }

  // Find active campaign for user
  const campaign = await AutonomousJob.findOne({
    user: req.user.id,
    status: ['active', 'paused']
  });

  if (!campaign) {
    return next(new AppError('No active or paused campaign found', 404));
  }

  // Toggle headless mode
  const result = await jobApplicationAgentService.toggleHeadlessMode(req.user.id, showBrowser);

  if (!result.success) {
    return next(new AppError(result.error || 'Failed to toggle browser visibility', 400));
  }

  // Update viewApplicationSubmission setting
  campaign.viewApplicationSubmission = showBrowser;
  await campaign.save();

  res.status(200).json({
    status: 'success',
    message: `Browser visibility ${showBrowser ? 'enabled' : 'disabled'} successfully`,
    data: {
      viewApplicationSubmission: showBrowser
    }
  });
});

// Get applications for a campaign
exports.getAgenticCampaignApplications = catchAsync(async (req, res, next) => {
  // Get the autonomous job campaign
  const campaign = await AutonomousJob.findById(req.params.id);

  if (!campaign) {
    return next(new AppError('No campaign found with that ID', 404));
  }

  // Check if campaign belongs to the current user
  if (campaign.user.toString() !== req.user.id) {
    return next(new AppError('You do not have permission to access this campaign', 403));
  }

  // Get applications from the agent service
  const agentResult = await jobApplicationAgentService.getCampaignApplications(req.user.id);

  // Also get applications from the database
  const dbApplications = await Application.find({
    user: req.user.id,
    autonomousJobCampaign: campaign._id
  });

  // Combine results
  const combinedApplications = [
    ...(agentResult.success ? agentResult.applications || [] : []),
    ...dbApplications
  ];

  // Remove duplicates
  const uniqueApplications = combinedApplications.reduce((acc, app) => {
    const jobUrl = app.jobUrl || (app.job && app.job.url);

    if (jobUrl && !acc.some(a => (a.jobUrl || (a.job && a.job.url)) === jobUrl)) {
      acc.push(app);
    }

    return acc;
  }, []);

  res.status(200).json({
    status: 'success',
    results: uniqueApplications.length,
    data: {
      applications: uniqueApplications
    }
  });
});

// Update campaign configuration
exports.updateAgenticCampaignConfig = catchAsync(async (req, res, next) => {
  // Get the autonomous job campaign
  const campaign = await AutonomousJob.findById(req.params.id);

  if (!campaign) {
    return next(new AppError('No campaign found with that ID', 404));
  }

  // Check if campaign belongs to the current user
  if (campaign.user.toString() !== req.user.id) {
    return next(new AppError('You do not have permission to update this campaign', 403));
  }

  // Update the campaign in database
  const updatedCampaign = await AutonomousJob.findByIdAndUpdate(
    req.params.id,
    req.body,
    {
      new: true,
      runValidators: true
    }
  );

  // Update the running campaign configuration
  const result = await jobApplicationAgentService.updateCampaignConfig(req.user.id, req.body);

  if (!result.success) {
    return next(new AppError(result.error || 'Failed to update campaign configuration', 400));
  }

  res.status(200).json({
    status: 'success',
    message: 'Campaign configuration updated successfully',
    data: {
      campaign: updatedCampaign
    }
  });
});

// Get campaign status and statistics
exports.getAgenticCampaignStatus = catchAsync(async (req, res, next) => {
  // Get the autonomous job campaign
  const campaign = await AutonomousJob.findById(req.params.id);

  if (!campaign) {
    return next(new AppError('No campaign found with that ID', 404));
  }

  // Check if campaign belongs to the current user
  if (campaign.user.toString() !== req.user.id) {
    return next(new AppError('You do not have permission to access this campaign', 403));
  }

  // Get applications from the database
  const applications = await Application.find({
    user: req.user.id,
    autonomousJobCampaign: campaign._id
  });

  // Calculate status distribution
  const statusDistribution = {};
  applications.forEach(app => {
    if (!statusDistribution[app.status]) {
      statusDistribution[app.status] = 0;
    }
    statusDistribution[app.status]++;
  });

  // Check if running in the agent service
  const isRunningInAgent = jobApplicationAgentService.runningAgents.has(req.user.id);

  res.status(200).json({
    status: 'success',
    data: {
      campaignStatus: campaign.status,
      isRunningInAgent,
      viewApplicationSubmission: campaign.viewApplicationSubmission || false,
      statistics: campaign.statistics,
      statusDistribution,
      totalApplications: applications.length,
      startDate: campaign.startDate,
      endDate: campaign.endDate,
      lastRunDate: campaign.lastRunDate
    }
  });
});
